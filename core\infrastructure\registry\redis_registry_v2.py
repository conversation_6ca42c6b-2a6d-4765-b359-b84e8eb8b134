"""
Enhanced Redis Service Registry with Fallback Mechanisms

This module provides a Redis-backed service registry with comprehensive
fallback mechanisms for handling Redis Cloud free tier limits.

Features:
- Connection pooling and rate limiting
- Memory fallback when Redis is unavailable
- Queue system for retrying failed operations
- Graceful degradation under load
- Distributed locking with fallback
- Health monitoring with circuit breaker pattern

Migrated from: core.infrastructure.redis_service_registry_v2
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, Optional
from urllib.parse import urljoin

import aiohttp

from ...domain.interfaces import IServiceRegistry
from .models import ServiceInfo

# Import fallback manager from modular location
try:
    from ..cache.fallback import (
        RedisFallbackManager,
        RedisLimits,
        FallbackStrategy
    )
except ImportError:
    # Fallback to legacy location
    try:
        from ..redis_fallback_manager import (
            RedisFallbackManager,
            RedisLimits,
            FallbackStrategy
        )
    except ImportError:
        # Final fallback for development
        RedisFallbackManager = None
        RedisLimits = None
        FallbackStrategy = None

logger = logging.getLogger(__name__)


class EnhancedRedisServiceRegistry(IServiceRegistry):
    """
    Enhanced Redis service registry with fallback mechanisms for free tier limits

    Features:
    - Connection pooling and rate limiting
    - Memory fallback when Redis is unavailable
    - Queue system for retrying failed operations
    - Graceful degradation under load
    - Distributed locking with fallback
    - Health monitoring with circuit breaker pattern
    """

    def __init__(
        self,
        redis_url: str,
        key_prefix: str = "service_registry:",
        default_ttl: int = 300,
        health_check_interval: int = 30,
        lock_timeout: int = 10,
        fallback_strategy: Optional[Any] = None,  # FallbackStrategy type
        redis_limits: Optional[Any] = None  # RedisLimits type
    ):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.health_check_interval = health_check_interval
        self.lock_timeout = lock_timeout
        self.fallback_strategy = fallback_strategy or (FallbackStrategy.HYBRID if FallbackStrategy else None)

        # Initialize fallback manager if available
        if RedisFallbackManager and RedisLimits:
            self.fallback_manager = RedisFallbackManager(
                redis_url=redis_url,
                limits=redis_limits or RedisLimits(),
                fallback_strategy=self.fallback_strategy,
                enable_memory_fallback=True,
                enable_queue_system=True
            )
        else:
            self.fallback_manager = None

        # Memory fallback for service registry
        self._memory_services: Dict[str, ServiceInfo] = {}
        self._memory_locks: Dict[str, Dict[str, Any]] = {}

        # Health monitoring
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._running = False
        self._initialized = False

    async def initialize(self):
        """Initialize the service registry"""
        if not self._initialized:
            if self.fallback_manager:
                await self.fallback_manager.initialize()
            self._initialized = True
            logger.info("Redis service registry V2 initialized")

    def _make_service_key(self, service_name: str) -> str:
        """Create Redis key for service"""
        return f"{self.key_prefix}services:{service_name}"

    def _make_lock_key(self, service_name: str) -> str:
        """Create Redis key for service lock"""
        return f"{self.key_prefix}locks:{service_name}"

    def _make_heartbeat_key(self, service_name: str) -> str:
        """Create Redis key for service heartbeat"""
        return f"{self.key_prefix}heartbeats:{service_name}"

    async def register_service(
        self,
        name: str,
        url: str,
        version: str,
        health_endpoint: str = "/health",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a new service with fallback mechanisms"""
        if not self._initialized:
            await self.initialize()

        try:
            # Generate API key for the service
            api_key = f"api_key_{name}_{int(time.time())}_{uuid.uuid4().hex[:8]}"

            # Create service info
            now = datetime.now(timezone.utc)
            service_info = ServiceInfo(
                name=name,
                url=url,
                version=version,
                health_endpoint=health_endpoint,
                api_key=api_key,
                metadata=metadata or {},
                registered_at=now,
                last_heartbeat=now,
                ttl=self.default_ttl
            )

            # Store service in Redis with fallback
            service_key = self._make_service_key(name)
            service_data = service_info.model_dump_json()

            try:
                if self.fallback_manager:
                    await self.fallback_manager.execute_operation(
                        "setex",
                        service_key,
                        self.default_ttl,
                        service_data,
                        fallback_value=True,
                        ttl=self.default_ttl,
                        priority=2
                    )
            except Exception as e:
                logger.warning(f"Redis registration failed for {name}: {e}")

            # Always store in memory as backup
            self._memory_services[name] = service_info

            return {
                "success": True,
                "service": name,
                "api_key": api_key,
                "ttl": self.default_ttl,
                "heartbeat_interval": self.health_check_interval
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to register service: {str(e)}"
            }

    async def get_service(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get service information with fallback"""
        if not self._initialized:
            await self.initialize()

        service_key = self._make_service_key(service_name)

        # Try Redis first
        try:
            if self.fallback_manager:
                service_data = await self.fallback_manager.execute_operation(
                    "get",
                    service_key,
                    fallback_value=None
                )

                if service_data:
                    if isinstance(service_data, bytes):
                        service_data = service_data.decode('utf-8')

                    service_info = ServiceInfo.model_validate_json(service_data)
                    return service_info.model_dump()

        except Exception as e:
            logger.warning(f"Failed to get service {service_name} from Redis: {e}")

        # Fallback to memory
        if service_name in self._memory_services:
            return self._memory_services[service_name].model_dump()

        return None

    async def list_services(self, only_healthy: bool = False) -> Dict[str, Any]:
        """List all registered services with fallback"""
        if not self._initialized:
            await self.initialize()

        services = {}

        # Try to get services from Redis first
        try:
            if self.fallback_manager:
                pattern = f"{self.key_prefix}services:*"
                keys = await self.fallback_manager.execute_operation(
                    "keys",
                    pattern,
                    fallback_value=[]
                )

                if keys:
                    for key in keys:
                        try:
                            service_data = await self.fallback_manager.execute_operation(
                                "get",
                                key,
                                fallback_value=None
                            )

                            if service_data:
                                if isinstance(service_data, bytes):
                                    service_data = service_data.decode('utf-8')

                                service_info = ServiceInfo.model_validate_json(service_data)
                                services[service_info.name] = service_info.model_dump()

                        except Exception as e:
                            logger.warning(f"Failed to parse service data for key {key}: {e}")

        except Exception as e:
            logger.warning(f"Failed to list services from Redis: {e}")

        # Add services from memory fallback
        for name, service_info in self._memory_services.items():
            if name not in services:
                services[name] = service_info.model_dump()

        # Filter by health status if requested
        all_services = list(services.values())
        if only_healthy:
            filtered_services = [s for s in all_services if s.get("is_healthy", True)]
            return {"services": {s["name"]: s for s in filtered_services}}

        return {"services": services}

    async def unregister_service(self, service_name: str) -> Dict[str, Any]:
        """Unregister a service with fallback mechanisms"""
        if not self._initialized:
            await self.initialize()

        try:
            service_key = self._make_service_key(service_name)
            heartbeat_key = self._make_heartbeat_key(service_name)

            # Check if service exists (try Redis first, then memory)
            service_exists = False

            try:
                if self.fallback_manager:
                    exists_result = await self.fallback_manager.execute_operation(
                        "exists",
                        service_key,
                        fallback_value=False
                    )
                    service_exists = bool(exists_result)
            except Exception:
                pass

            if not service_exists:
                service_exists = service_name in self._memory_services

            if not service_exists:
                return {"success": False, "error": "Service not found"}

            # Remove service from Redis
            try:
                if self.fallback_manager:
                    await self.fallback_manager.execute_operation(
                        "delete",
                        service_key,
                        heartbeat_key,
                        fallback_value=True,
                        priority=2
                    )
            except Exception as e:
                logger.warning(f"Redis unregistration failed for {service_name}: {e}")

            # Always remove from memory
            self._memory_services.pop(service_name, None)

            return {"success": True, "service": service_name}

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to unregister service: {str(e)}"
            }

    async def call_service(
        self,
        service_name: str,
        endpoint: str,
        method: str = "GET",
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Call a service endpoint"""
        service = await self.get_service(service_name)
        if not service:
            return {"success": False, "error": f"Service '{service_name}' not found"}

        try:
            url = urljoin(service["url"], endpoint.lstrip("/"))

            # Merge headers
            request_headers = {}
            if service.get("api_key"):
                request_headers["Authorization"] = f"Bearer {service['api_key']}"
            if headers:
                request_headers.update(headers)

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.request(method, url, json=data, headers=request_headers) as response:
                    result = {
                        "success": response.status < 400,
                        "status_code": response.status,
                        "url": url
                    }

                    try:
                        result["data"] = await response.json()
                    except Exception:
                        result["data"] = await response.text()

                    return result

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to call service: {str(e)}"
            }

    async def heartbeat(self, service_name: str) -> Dict[str, Any]:
        """Update service heartbeat with fallback"""
        if not self._initialized:
            await self.initialize()

        try:
            heartbeat_key = self._make_heartbeat_key(service_name)
            now = datetime.now(timezone.utc)

            # Update heartbeat in Redis
            try:
                if self.fallback_manager:
                    await self.fallback_manager.execute_operation(
                        "setex",
                        heartbeat_key,
                        self.default_ttl,
                        now.isoformat(),
                        fallback_value=True,
                        ttl=self.default_ttl,
                        priority=1
                    )
            except Exception as e:
                logger.warning(f"Redis heartbeat failed for {service_name}: {e}")

            # Update in memory
            if service_name in self._memory_services:
                self._memory_services[service_name].last_heartbeat = now

            return {
                "success": True,
                "service": service_name,
                "next_heartbeat": (now + timedelta(seconds=self.health_check_interval)).isoformat()
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to update heartbeat: {str(e)}"
            }

    async def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """Get service health status"""
        service = await self.get_service(service_name)
        if not service:
            return {"success": False, "error": f"Service '{service_name}' not found"}

        try:
            health_url = urljoin(service["url"], service["health_endpoint"].lstrip("/"))

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(health_url) as response:
                    is_healthy = response.status == 200

                    # Update health status in memory
                    if service_name in self._memory_services:
                        self._memory_services[service_name].is_healthy = is_healthy
                        self._memory_services[service_name].last_health_check = datetime.now(timezone.utc)

                    return {
                        "success": True,
                        "service": service_name,
                        "healthy": is_healthy,
                        "status_code": response.status,
                        "url": health_url
                    }

        except Exception as e:
            # Mark as unhealthy on error
            if service_name in self._memory_services:
                self._memory_services[service_name].is_healthy = False
                self._memory_services[service_name].last_health_check = datetime.now(timezone.utc)

            return {
                "success": False,
                "service": service_name,
                "healthy": False,
                "error": str(e)
            }

    async def restart_service(self, service_name: str) -> Dict[str, Any]:
        """Restart a registered service"""
        service = await self.get_service(service_name)
        if not service:
            return {"success": False, "error": f"Service '{service_name}' not found"}

        try:
            restart_url = urljoin(service["url"], service.get("restart_endpoint", "/admin/restart").lstrip("/"))

            request_headers = {}
            if service.get("api_key"):
                request_headers["Authorization"] = f"Bearer {service['api_key']}"

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.post(restart_url, headers=request_headers) as response:
                    return {
                        "success": response.status < 400,
                        "service": service_name,
                        "status_code": response.status,
                        "url": restart_url,
                        "message": "Restart command sent"
                    }

        except Exception as e:
            return {
                "success": False,
                "service": service_name,
                "error": f"Failed to restart service: {str(e)}"
            }


__all__ = [
    "EnhancedRedisServiceRegistry",
]
