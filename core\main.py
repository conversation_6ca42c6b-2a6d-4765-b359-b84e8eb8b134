"""
Main Application Entry Point

This module provides the main entry point for the FastAPI application
using the new modular architecture with dependency injection and plugin system.

Features:
- Modular architecture with dependency injection
- Plugin system for extensibility
- Supabase ecosystem integration
- Redis-based distributed services with fallback
- Comprehensive error handling and structured logging
"""

import logging
import sys
from typing import Optional

import uvicorn
from fastapi import FastAPI

from core.application.factory import create_application
from core.config.settings import create_settings, Settings

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('core.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)


def create_app(settings: Optional[Settings] = None) -> FastAPI:
    """Create FastAPI application using the new modular architecture

    Args:
        settings: Optional settings instance. If None, will load from environment

    Returns:
        Configured FastAPI application instance

    Raises:
        RuntimeError: If application creation fails
    """
    try:
        # Load settings if not provided
        if settings is None:
            settings = create_settings()

        logger.info(f"Creating {settings.APP_NAME} v{settings.APP_VERSION}")
        logger.info(f"Environment: {settings.app.environment.value}")
        logger.info(f"Debug mode: {settings.is_development()}")

        # Create application using factory
        app = create_application(settings)

        logger.info("✅ Application created successfully")
        return app

    except Exception as e:
        logger.error(f"❌ Failed to create application: {e}")
        raise RuntimeError(f"Application creation failed: {e}") from e


# Create app instance
app = create_app()


def run_server(settings: Optional[Settings] = None) -> None:
    """Run the development server with proper configuration

    Args:
        settings: Optional settings instance. If None, will load from environment
    """
    try:
        # Load settings if not provided
        if settings is None:
            settings = create_settings()

        logger.info(f"🚀 Starting {settings.APP_NAME} development server...")
        logger.info(f"Host: {settings.app.host}:{settings.app.port}")
        logger.info(f"Workers: {1 if settings.is_development() else settings.app.workers}")
        logger.info(f"Reload: {settings.is_development()}")

        # Run development server
        uvicorn.run(
            "core.main:app",
            host=settings.app.host,
            port=settings.app.port,
            reload=settings.is_development(),
            workers=1 if settings.is_development() else settings.app.workers,
            log_level=settings.logging.log_level.value.lower(),
            access_log=True,
        )

    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_server()